"""
ESP32数据接收客户端 - 从腾讯云服务器获取实时数据
可以在任何电脑上运行，获取ESP32设备上传的热成像和摄像头数据
"""

import requests
import json
import time
import base64
import numpy as np
import cv2
from PIL import Image, ImageTk
import tkinter as tk
from tkinter import ttk, messagebox
import threading
import queue
from datetime import datetime
import io
import socketio
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import os

class ESP32DataReceiver:
    def __init__(self):
        # 服务器配置
        self.server_url = "http://129.204.27.129:5000"  # 📝 替换为你的腾讯云服务器地址
        self.connected = False
        self.receiving = False
        
        # WebSocket客户端
        self.sio = socketio.Client()
        self.setup_socketio_events()
        
        # 数据存储
        self.latest_thermal_data = None
        self.latest_camera_image = None
        self.thermal_history = []
        self.camera_history = []
        
        # 统计信息
        self.stats = {
            'thermal_received': 0,
            'camera_received': 0,
            'connection_start': None,
            'last_update': None
        }
        
        # 数据队列（用于实时更新）
        self.thermal_queue = queue.Queue()
        self.camera_queue = queue.Queue()
        
        # 保存配置
        self.save_enabled = False
        self.save_directory = "received_esp32_data"
        
        # 创建GUI
        self.setup_gui()
        
        # 创建保存目录
        if not os.path.exists(self.save_directory):
            os.makedirs(self.save_directory)
    
    def setup_gui(self):
        """设置GUI界面"""
        self.root = tk.Tk()
        self.root.title("ESP32数据接收客户端 - 腾讯云版")
        self.root.geometry("1400x900")
        self.root.configure(bg='#f0f0f0')
        
        # 主标题
        title_frame = ttk.Frame(self.root)
        title_frame.pack(pady=10)
        title_label = ttk.Label(title_frame, text="📡 ESP32数据接收客户端 - 腾讯云数据源", 
                               font=("Arial", 18, "bold"))
        title_label.pack()
        
        # 连接配置面板
        config_frame = ttk.LabelFrame(self.root, text="☁️ 服务器连接", padding=10)
        config_frame.pack(fill='x', padx=10, pady=5)
        
        # 服务器配置行
        server_row = ttk.Frame(config_frame)
        server_row.pack(fill='x', pady=5)
        
        ttk.Label(server_row, text="服务器地址:", font=("Arial", 12)).pack(side='left', padx=(0,10))
        self.server_url_var = tk.StringVar(value=self.server_url)
        server_entry = ttk.Entry(server_row, textvariable=self.server_url_var, width=50, font=("Arial", 11))
        server_entry.pack(side='left', padx=(0,10))
        
        self.connect_btn = ttk.Button(server_row, text="🔗 连接服务器", command=self.toggle_connection)
        self.connect_btn.pack(side='left', padx=(0,10))
        
        self.status_label = ttk.Label(server_row, text="状态: 未连接", foreground="red", font=("Arial", 12))
        self.status_label.pack(side='left')
        
        # 控制面板
        control_row = ttk.Frame(config_frame)
        control_row.pack(fill='x', pady=5)
        
        self.realtime_var = tk.BooleanVar()
        realtime_cb = ttk.Checkbutton(control_row, text="📡 实时接收", 
                                     variable=self.realtime_var,
                                     command=self.toggle_realtime_receiving)
        realtime_cb.pack(side='left', padx=(0,15))
        
        ttk.Button(control_row, text="📥 获取最新数据", command=self.get_latest_data).pack(side='left', padx=(0,10))
        ttk.Button(control_row, text="🌐 打开监控面板", command=self.open_dashboard).pack(side='left', padx=(0,10))
        ttk.Button(control_row, text="📊 服务器状态", command=self.show_server_status).pack(side='left', padx=(0,10))
        
        self.save_var = tk.BooleanVar()
        save_cb = ttk.Checkbutton(control_row, text="💾 自动保存", variable=self.save_var)
        save_cb.pack(side='left', padx=(15,0))
        
        # 显示区域
        display_frame = ttk.Frame(self.root)
        display_frame.pack(expand=True, fill='both', padx=10, pady=5)
        
        # 左侧：热成像显示
        thermal_frame = ttk.LabelFrame(display_frame, text="🌡️ 热成像数据", padding=5)
        thermal_frame.pack(side='left', expand=True, fill='both', padx=(0,5))
        
        # 热成像画布
        self.thermal_fig, self.thermal_ax = plt.subplots(figsize=(6, 4))
        self.thermal_canvas = FigureCanvasTkAgg(self.thermal_fig, thermal_frame)
        self.thermal_canvas.get_tk_widget().pack(expand=True, fill='both')
        
        # 热成像信息
        self.thermal_info_label = ttk.Label(thermal_frame, text="等待热成像数据...", font=("Arial", 10))
        self.thermal_info_label.pack(pady=5)
        
        # 右侧：摄像头显示
        camera_frame = ttk.LabelFrame(display_frame, text="📷 摄像头图像", padding=5)
        camera_frame.pack(side='right', expand=True, fill='both', padx=(5,0))
        
        # 摄像头显示
        self.camera_label = tk.Label(camera_frame, bg='black', text="等待摄像头数据...\n\n📡 请先连接服务器并启用实时接收", 
                                    font=("Arial", 12), fg="white")
        self.camera_label.pack(expand=True, fill='both')
        
        # 信息面板
        info_frame = ttk.LabelFrame(self.root, text="📊 接收状态", padding=5)
        info_frame.pack(fill='x', padx=10, pady=5)
        
        # 统计信息
        stats_row1 = ttk.Frame(info_frame)
        stats_row1.pack(fill='x', pady=2)
        
        self.connection_info = ttk.Label(stats_row1, text="连接信息: 未连接", font=("Arial", 11))
        self.connection_info.pack(side='left')
        
        self.update_time_label = ttk.Label(stats_row1, text="最后更新: --", font=("Arial", 11))
        self.update_time_label.pack(side='right')
        
        stats_row2 = ttk.Frame(info_frame)
        stats_row2.pack(fill='x', pady=2)
        
        self.stats_label = ttk.Label(stats_row2, text="接收统计: 热成像=0, 摄像头=0", font=("Arial", 11))
        self.stats_label.pack(side='left')
        
        self.realtime_status = ttk.Label(stats_row2, text="实时接收: 未启用", font=("Arial", 11))
        self.realtime_status.pack(side='right')
        
        # 启动信息更新
        self.update_info_display()
    
    def setup_socketio_events(self):
        """设置WebSocket事件处理"""
        
        @self.sio.event
        def connect():
            print("🔗 WebSocket连接成功")
            self.connected = True
            self.stats['connection_start'] = time.time()
            self.root.after(0, lambda: self.status_label.config(text="状态: 已连接", foreground="green"))
        
        @self.sio.event
        def disconnect():
            print("🔗 WebSocket连接断开")
            self.connected = False
            self.root.after(0, lambda: self.status_label.config(text="状态: 连接断开", foreground="red"))
        
        @self.sio.event
        def thermal_update(data):
            """接收热成像数据更新"""
            try:
                thermal_array = np.array(data['data'])
                timestamp = data.get('timestamp', time.time())
                
                # 添加到队列和历史
                self.thermal_queue.put({
                    'data': thermal_array,
                    'timestamp': timestamp,
                    'min_temp': data.get('min_temp', 0),
                    'max_temp': data.get('max_temp', 0),
                    'avg_temp': data.get('avg_temp', 0)
                })
                
                self.latest_thermal_data = thermal_array
                self.thermal_history.append({
                    'timestamp': timestamp,
                    'min_temp': data.get('min_temp', 0),
                    'max_temp': data.get('max_temp', 0),
                    'avg_temp': data.get('avg_temp', 0)
                })
                
                # 限制历史记录长度
                if len(self.thermal_history) > 100:
                    self.thermal_history.pop(0)
                
                self.stats['thermal_received'] += 1
                self.stats['last_update'] = time.time()
                
                print(f"🌡️ 接收热成像数据: {data.get('min_temp', 0):.1f}~{data.get('max_temp', 0):.1f}°C")
                
                # 更新显示
                self.root.after(0, self.update_thermal_display)
                
            except Exception as e:
                print(f"❌ 处理热成像数据错误: {e}")
        
        @self.sio.event
        def camera_update(data):
            """接收摄像头数据更新"""
            try:
                image_data = data['image_data']
                timestamp = data.get('timestamp', time.time())
                
                # 解码图像
                image_bytes = base64.b64decode(image_data)
                image = Image.open(io.BytesIO(image_bytes))
                
                self.camera_queue.put({
                    'image': image,
                    'timestamp': timestamp,
                    'size': data.get('size', 0)
                })
                
                self.latest_camera_image = image
                self.camera_history.append({
                    'timestamp': timestamp,
                    'size': data.get('size', 0)
                })
                
                # 限制历史记录长度
                if len(self.camera_history) > 50:
                    self.camera_history.pop(0)
                
                self.stats['camera_received'] += 1
                self.stats['last_update'] = time.time()
                
                print(f"📷 接收摄像头数据: {data.get('size', 0)} bytes")
                
                # 更新显示
                self.root.after(0, self.update_camera_display)
                
                # 自动保存
                if self.save_var.get():
                    self.save_received_data()
                
            except Exception as e:
                print(f"❌ 处理摄像头数据错误: {e}")
    
    def toggle_connection(self):
        """切换服务器连接"""
        if not self.connected:
            self.connect_to_server()
        else:
            self.disconnect_from_server()
    
    def connect_to_server(self):
        """连接到服务器"""
        try:
            server_url = self.server_url_var.get().strip()
            if not server_url:
                messagebox.showerror("错误", "请输入服务器地址")
                return
            
            self.server_url = server_url
            
            # 测试HTTP连接
            response = requests.get(f"{server_url}/", timeout=10)
            if response.status_code == 200:
                print("✅ HTTP连接测试成功")
                
                # 连接WebSocket
                self.sio.connect(server_url)
                
                self.connect_btn.config(text="🔌 断开连接")
                messagebox.showinfo("连接成功", f"已成功连接到ESP32云端服务器\n🌐 地址: {server_url}")
                
            else:
                messagebox.showerror("连接失败", f"服务器响应错误: {response.status_code}")
                
        except requests.exceptions.Timeout:
            messagebox.showerror("连接失败", "连接超时，请检查服务器地址")
        except Exception as e:
            messagebox.showerror("连接失败", f"连接错误: {str(e)}")
    
    def disconnect_from_server(self):
        """断开服务器连接"""
        self.toggle_realtime_receiving(force_stop=True)
        
        if self.sio.connected:
            self.sio.disconnect()
        
        self.connected = False
        self.connect_btn.config(text="🔗 连接服务器")
        self.status_label.config(text="状态: 未连接", foreground="red")
        print("🔗 已断开服务器连接")
    
    def toggle_realtime_receiving(self, force_stop=False):
        """切换实时接收"""
        if force_stop:
            self.receiving = False
            self.realtime_var.set(False)
            return
        
        self.receiving = self.realtime_var.get()
        
        if self.receiving:
            if not self.connected:
                messagebox.showerror("错误", "请先连接服务器")
                self.realtime_var.set(False)
                return
            
            # 加入数据流
            self.sio.emit('join_thermal')
            self.sio.emit('join_camera')
            
            self.realtime_status.config(text="实时接收: ✅ 已启用", foreground="green")
            print("📡 已启用实时数据接收")
            
        else:
            # 离开数据流
            if self.connected:
                self.sio.emit('leave_thermal')
                self.sio.emit('leave_camera')
            
            self.realtime_status.config(text="实时接收: ❌ 未启用", foreground="red")
            print("📡 已停止实时数据接收")
    
    def get_latest_data(self):
        """获取最新数据（单次请求）"""
        if not self.server_url:
            messagebox.showerror("错误", "请先设置服务器地址")
            return
        
        try:
            # 获取热成像数据
            response = requests.get(f"{self.server_url}/api/data/thermal/latest", timeout=10)
            if response.status_code == 200:
                thermal_data = response.json()['data']
                thermal_array = np.array(thermal_data['data'])
                self.latest_thermal_data = thermal_array
                self.update_thermal_display()
                print("✅ 获取最新热成像数据成功")
            
            # 获取摄像头数据
            response = requests.get(f"{self.server_url}/api/data/camera/latest", timeout=10)
            if response.status_code == 200:
                camera_data = response.json()['data']
                image_bytes = base64.b64decode(camera_data['image_data'])
                image = Image.open(io.BytesIO(image_bytes))
                self.latest_camera_image = image
                self.update_camera_display()
                print("✅ 获取最新摄像头数据成功")
                
            messagebox.showinfo("成功", "已获取最新数据")
            
        except Exception as e:
            messagebox.showerror("获取失败", f"获取数据失败: {str(e)}")
    
    def update_thermal_display(self):
        """更新热成像显示"""
        if self.latest_thermal_data is None:
            return
        
        try:
            # 清除之前的图像
            self.thermal_ax.clear()
            
            # 绘制热成像
            im = self.thermal_ax.imshow(self.latest_thermal_data, cmap='hot', interpolation='nearest')
            self.thermal_ax.set_title('实时热成像数据', fontsize=12)
            
            # 添加颜色条
            if hasattr(self, 'thermal_colorbar'):
                self.thermal_colorbar.remove()
            self.thermal_colorbar = self.thermal_fig.colorbar(im, ax=self.thermal_ax, label='温度 (°C)')
            
            # 更新画布
            self.thermal_canvas.draw()
            
            # 更新信息标签
            min_temp = np.nanmin(self.latest_thermal_data)
            max_temp = np.nanmax(self.latest_thermal_data)
            avg_temp = np.nanmean(self.latest_thermal_data)
            
            self.thermal_info_label.config(
                text=f"温度范围: {min_temp:.1f}°C ~ {max_temp:.1f}°C | 平均: {avg_temp:.1f}°C"
            )
            
        except Exception as e:
            print(f"❌ 更新热成像显示错误: {e}")
    
    def update_camera_display(self):
        """更新摄像头显示"""
        if self.latest_camera_image is None:
            return
        
        try:
            # 计算适合的显示尺寸
            display_width = self.camera_label.winfo_width()
            display_height = self.camera_label.winfo_height()
            
            if display_width > 100 and display_height > 100:
                # 保持宽高比缩放
                scale_w = (display_width * 0.9) / self.latest_camera_image.size[0]
                scale_h = (display_height * 0.9) / self.latest_camera_image.size[1]
                scale = min(scale_w, scale_h)
                
                new_width = int(self.latest_camera_image.size[0] * scale)
                new_height = int(self.latest_camera_image.size[1] * scale)
                
                resized_image = self.latest_camera_image.resize((new_width, new_height), Image.Resampling.LANCZOS)
                
                photo = ImageTk.PhotoImage(resized_image)
                self.camera_label.config(image=photo, text="")
                self.camera_label.image = photo
                
        except Exception as e:
            print(f"❌ 更新摄像头显示错误: {e}")
    
    def save_received_data(self):
        """保存接收到的数据"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            # 保存热成像数据
            if self.latest_thermal_data is not None:
                np.save(f"{self.save_directory}/received_thermal_{timestamp}.npy", self.latest_thermal_data)
                
                # 保存热成像图像
                plt.figure(figsize=(10, 8))
                plt.imshow(self.latest_thermal_data, cmap='hot', interpolation='nearest')
                plt.colorbar(label='温度 (°C)')
                plt.title(f'接收的热成像数据 - {timestamp}')
                plt.savefig(f"{self.save_directory}/received_thermal_{timestamp}.png", dpi=150)
                plt.close()
            
            # 保存摄像头图像
            if self.latest_camera_image is not None:
                self.latest_camera_image.save(f"{self.save_directory}/received_camera_{timestamp}.jpg", quality=95)
            
            print(f"💾 数据保存成功: {timestamp}")
            
        except Exception as e:
            print(f"❌ 保存数据失败: {e}")
    
    def open_dashboard(self):
        """打开监控面板"""
        try:
            import webbrowser
            webbrowser.open(self.server_url)
        except Exception as e:
            messagebox.showerror("错误", f"无法打开浏览器: {str(e)}")
    
    def show_server_status(self):
        """显示服务器状态"""
        try:
            response = requests.get(f"{self.server_url}/api/stats", timeout=10)
            if response.status_code == 200:
                stats = response.json()
                
                status_info = f"""
📊 ESP32服务器状态
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
🌐 服务器地址: {self.server_url}
⏱️ 服务器运行时间: {stats.get('server_uptime', 'unknown')}

📡 ESP32设备状态:
  🔗 设备在线: {'✅ 是' if stats.get('device_status', {}).get('is_online') else '❌ 否'}
  🌡️ 热成像帧数: {stats.get('device_status', {}).get('thermal_count', 0)}
  📷 摄像头帧数: {stats.get('device_status', {}).get('camera_count', 0)}

💻 系统资源:
  🔥 CPU使用率: {stats.get('system_stats', {}).get('cpu_percent', 0):.1f}%
  💾 内存使用率: {stats.get('system_stats', {}).get('memory_percent', 0):.1f}%
  💿 磁盘使用率: {stats.get('system_stats', {}).get('disk_percent', 0):.1f}%

📊 数据队列:
  🌡️ 热成像队列: {stats.get('queue_stats', {}).get('thermal_queue_size', 0)}/{stats.get('queue_stats', {}).get('thermal_queue_max', 0)}
  📷 摄像头队列: {stats.get('queue_stats', {}).get('camera_queue_size', 0)}/{stats.get('queue_stats', {}).get('camera_queue_max', 0)}

🔗 连接客户端:
  👥 总连接数: {stats.get('client_stats', {}).get('total_clients', 0)}
  🌡️ 热成像客户端: {stats.get('client_stats', {}).get('thermal_clients', 0)}
  📷 摄像头客户端: {stats.get('client_stats', {}).get('camera_clients', 0)}
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
                """
                
                messagebox.showinfo("服务器状态", status_info)
            else:
                messagebox.showerror("错误", f"无法获取服务器状态: {response.status_code}")
                
        except Exception as e:
            messagebox.showerror("错误", f"获取服务器状态失败: {str(e)}")
    
    def update_info_display(self):
        """更新信息显示"""
        # 连接信息
        if self.connected:
            if self.stats['connection_start']:
                uptime = int(time.time() - self.stats['connection_start'])
                hours = uptime // 3600
                minutes = (uptime % 3600) // 60
                seconds = uptime % 60
                connection_text = f"连接信息: ✅ 已连接 ({hours:02d}:{minutes:02d}:{seconds:02d})"
            else:
                connection_text = "连接信息: ✅ 已连接"
        else:
            connection_text = "连接信息: ❌ 未连接"
        
        self.connection_info.config(text=connection_text)
        
        # 统计信息
        stats_text = f"接收统计: 热成像={self.stats['thermal_received']}, 摄像头={self.stats['camera_received']}"
        self.stats_label.config(text=stats_text)
        
        # 最后更新时间
        if self.stats['last_update']:
            last_update = datetime.fromtimestamp(self.stats['last_update']).strftime("%H:%M:%S")
            update_text = f"最后更新: {last_update}"
        else:
            update_text = "最后更新: --"
        
        self.update_time_label.config(text=update_text)
        
        # 继续更新
        self.root.after(1000, self.update_info_display)
    
    def run(self):
        """运行客户端"""
        try:
            self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
            print("📡 ESP32数据接收客户端启动")
            print(f"🌐 默认服务器: {self.server_url}")
            print("📌 请连接服务器并启用实时接收")
            self.root.mainloop()
        except KeyboardInterrupt:
            self.on_closing()
    
    def on_closing(self):
        """程序关闭处理"""
        print("🔻 正在关闭接收客户端...")
        self.disconnect_from_server()
        try:
            self.root.quit()
            self.root.destroy()
        except:
            pass

# 简化版API客户端（用于脚本集成）
class SimpleESP32DataClient:
    """
    简化版客户端，可直接集成到其他项目中
    """
    def __init__(self, server_url):
        self.server_url = server_url
        self.session = requests.Session()
    
    def get_latest_thermal(self):
        """获取最新热成像数据"""
        try:
            response = self.session.get(f"{self.server_url}/api/data/thermal/latest", timeout=5)
            if response.status_code == 200:
                data = response.json()['data']
                return {
                    'thermal_array': np.array(data['data']),
                    'timestamp': data['timestamp'],
                    'min_temp': data['min_temp'],
                    'max_temp': data['max_temp'],
                    'avg_temp': data['avg_temp']
                }
            else:
                return None
        except Exception as e:
            print(f"获取热成像数据失败: {e}")
            return None
    
    def get_latest_camera(self):
        """获取最新摄像头图像"""
        try:
            response = self.session.get(f"{self.server_url}/api/data/camera/latest", timeout=5)
            if response.status_code == 200:
                data = response.json()['data']
                image_bytes = base64.b64decode(data['image_data'])
                image = Image.open(io.BytesIO(image_bytes))
                
                return {
                    'image': image,
                    'timestamp': data['timestamp'],
                    'size': data['size']
                }
            else:
                return None
        except Exception as e:
            print(f"获取摄像头数据失败: {e}")
            return None
    
    def get_server_status(self):
        """获取服务器状态"""
        try:
            response = self.session.get(f"{self.server_url}/api/stats", timeout=5)
            if response.status_code == 200:
                return response.json()
            else:
                return None
        except Exception as e:
            print(f"获取服务器状态失败: {e}")
            return None

# 使用示例
def example_simple_usage():
    """简化客户端使用示例"""
    print("📡 ESP32简化客户端示例")
    
    # 创建客户端
    client = SimpleESP32DataClient("http://129.204.27.129:5000")  # 替换为你的服务器地址
    
    while True:
        try:
            # 获取最新热成像数据
            thermal_data = client.get_latest_thermal()
            if thermal_data:
                print(f"🌡️ 热成像: {thermal_data['min_temp']:.1f}~{thermal_data['max_temp']:.1f}°C")
            
            # 获取最新摄像头数据
            camera_data = client.get_latest_camera()
            if camera_data:
                print(f"📷 摄像头: {camera_data['image'].size}, {camera_data['size']} bytes")
            
            time.sleep(2)  # 2秒间隔
            
        except KeyboardInterrupt:
            break
        except Exception as e:
            print(f"错误: {e}")
            time.sleep(5)

if __name__ == "__main__":
    print("=" * 80)
    print("📡 ESP32数据接收客户端 - 腾讯云版")
    print("=" * 80)
    print("🎯 主要功能:")
    print("  📡 连接腾讯云ESP32服务器")
    print("  🔄 实时接收热成像和摄像头数据")
    print("  📊 实时数据可视化显示")
    print("  💾 自动保存接收的数据")
    print("  🌐 监控面板一键访问")
    print("=" * 80)
    print("📋 使用步骤:")
    print("  1️⃣ 确保ESP32服务器正在运行")
    print("  2️⃣ 配置正确的服务器地址")
    print("  3️⃣ 点击'连接服务器'")
    print("  4️⃣ 启用'实时接收'")
    print("  5️⃣ 查看实时数据显示")
    print("=" * 80)
    print("⚠️  依赖安装:")
    print("  pip install requests socketio numpy opencv-python pillow matplotlib")
    print("=" * 80)
    
    try:
        # 运行图形界面版本
        receiver = ESP32DataReceiver()
        receiver.run()
        
        # 运行简化版本（取消注释下面的行）
        # example_simple_usage()
        
    except Exception as e:
        print(f"❌ 客户端启动失败: {e}")
        input("按回车键退出...")